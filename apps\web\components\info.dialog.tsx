"use client";

import * as React from "react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";

export interface InfoDialogProps {
  // Dialog state
  open?: boolean;
  onOpenChange?: (open: boolean) => void;

  // Content props
  title?: string;
  subtitle?: string;
  items?: string[];

  // Button text
  closeButtonText?: string;

  // Event handlers
  onClose?: () => void;
}

export function InfoDialog({
  open = true,
  onOpenChange,
  title = "Bilgilendirme",
  subtitle,
  items = [],
  closeButtonText = "TAMAM",
  onClose,
}: InfoDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent showCloseButton={false} className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {subtitle && (
            <DialogDescription className="text-foreground text-center">
              {subtitle}
            </DialogDescription>
          )}
        </DialogHeader>

        <main className="bg-muted rounded-lg mt-3.5 mx-5 pl-8 pr-3 py-9 space-y-3 overflow-y-auto max-h-[300px] text-accent-foreground text-sm leading-7 scrollbar-thin ">
          {items.map((item, index) => (
            <div key={index} className="flex gap-2 items-start">
              <span className="mr-3 text-background bg-foreground -skew-x-16 mt-2 h-5 py-1 px-2.5 leading-0 text-shadow-none flex items-center">
                <span className="skew-x-16">{index + 1}</span>
              </span>{" "}
              <span className="fake-stroke-half "> {item}</span>
            </div>
          ))}
        </main>

        <DialogFooter className="flex justify-center">
          <DialogClose asChild>
            <Button size={"small"} className="w-fit px-5" onClick={onClose}>
              {closeButtonText}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
